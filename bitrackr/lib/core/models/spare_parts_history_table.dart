import 'package:drift/drift.dart';

/// Spare Parts History table definition for tracking replacement history.
/// Stores historical information about replaced parts and their usage.
@DataClassName('SparePartsHistoryData')
class SparePartsHistoryTable extends Table {
  /// Primary key UUID
  TextColumn get uuid => text()();

  /// Auto-increment ID for internal use
  IntColumn get id => integer().autoIncrement()();

  /// Name of the spare part
  TextColumn get partName => text()();

  /// Type/category of the spare part
  TextColumn get partType => text()();

  /// Price of the spare part
  RealColumn get price => real()();

  /// Date when the part was replaced
  DateTimeColumn get replacementDate => dateTime()();

  /// Vehicle mileage at the time of replacement
  IntColumn get mileageAtReplacement => integer()();

  /// Reference to the original spare part ID
  IntColumn get sparePartId => integer().references(SparePartsTable, #id)();

  /// Date when the part was originally installed
  DateTimeColumn get installationDate => dateTime()();

  /// Initial mileage when part was installed
  IntColumn get initialMileage => integer()();

  /// Reason for replacement
  TextColumn get replacementReason => text().withDefault(const Constant('Regular maintenance'))();

  /// ID of the part that replaced this one
  IntColumn get replacedByPartId => integer().nullable()();

  /// Replacement count number
  IntColumn get replacementCount => integer().withDefault(const Constant(1))();

  /// Number of days the part was in use (calculated)
  IntColumn get usageDays => integer().withDefault(const Constant(0))();

  /// Mileage the part was used for (calculated)
  IntColumn get usageMileage => integer().withDefault(const Constant(0))();

  /// Additional notes about the replacement
  TextColumn get notes => text().withDefault(const Constant(''))();

  /// Record creation timestamp
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();

  /// Record update timestamp
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();

  /// Soft delete timestamp
  DateTimeColumn get deletedAt => dateTime().nullable()();

  /// Sync status for cloud synchronization
  TextColumn get syncStatus => text().withDefault(const Constant('pendingUpload'))();

  @override
  Set<Column> get primaryKey => {uuid};

  @override
  List<String> get customConstraints => [
    'CHECK (price >= 0)',
    'CHECK (mileage_at_replacement >= initial_mileage)',
    'CHECK (replacement_count >= 1)',
    'CHECK (usage_days >= 0)',
    'CHECK (usage_mileage >= 0)',
  ];
}
